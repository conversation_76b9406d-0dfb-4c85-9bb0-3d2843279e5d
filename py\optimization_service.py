# -*- coding: utf-8 -*-
"""
烧结配料计算模型系统 - SQP优化算法服务
基于SQP二次序列规划算法，支持成本最优和质量最优双目标优化
作者:lzk
时间:2025-07-31
"""

import sys
import os
import locale
from typing import Dict, List, Tuple, Optional, Union
from dataclasses import dataclass
from enum import Enum

# 设置环境编码
os.environ['PYTHONIOENCODING'] = 'utf-8'
if sys.platform.startswith('win'):
    try:
        locale.setlocale(locale.LC_ALL, 'Chinese (Simplified)_China.utf8')
    except:
        pass

# 导入核心库
from flask import Flask, jsonify, request
try:
    from flask_cors import CORS
    CORS_AVAILABLE = True
except ImportError:
    CORS_AVAILABLE = False
    print("警告: flask_cors未安装，将使用手动CORS设置")

import numpy as np
from scipy.optimize import minimize
import datetime
import time
from functools import wraps

# 优化目标类型枚举
class OptimizationType(Enum):
    COST_OPTIMAL = "cost"
    QUALITY_OPTIMAL = "quality"

# 数据类定义
@dataclass
class MaterialData:
    """原料数据结构"""
    name: str
    tfe: float          # TFe含量(%)
    cao: float          # CaO含量(%)
    sio2: float         # SiO2含量(%)
    mgo: float          # MgO含量(%)
    al2o3: float        # Al2O3含量(%)
    h2o: float          # 水分(%)
    ig: float          # 烧损(%)
    price: float        # 价格(元/吨)
    min_ratio: float = 0.0    # 最小配比(%)
    max_ratio: float = 100.0  # 最大配比(%)

@dataclass
class OptimizationTarget:
    """优化目标参数"""
    tfe: float = 55.0
    r: float = 1.90      # 碱度
    mgo: float = 2.39
    al2o3: float = 1.89

@dataclass
class ConstraintRanges:
    """约束范围"""
    tfe: Tuple[float, float] = (54, 55)
    r: Tuple[float, float] = (1.88, 2.02)
    mgo: Tuple[float, float] = (1.6, 2.8)
    al2o3: Tuple[float, float] = (1.6, 2.0)
    cost: Tuple[float, float] = (400, 680)

@dataclass
class OptimizationResult:
    """优化结果"""
    success: bool
    ratios: Dict[str, float]
    properties: Dict[str, float]
    cost: float
    objective_value: float
    iterations: int
    message: str

# ======================== 核心优化算法类 ========================

class SinterOptimizer:
    """烧结配料SQP优化算法核心类"""

    def __init__(self):
        self.materials: List[MaterialData] = []
        self.targets = OptimizationTarget()
        self.constraints = ConstraintRanges()
        self.optimization_history = []

    def load_materials(self, materials_data: List[Dict]) -> None:
        """加载原料数据"""
        self.materials = []
        for data in materials_data:
            material = MaterialData(
                name=data['name'],
                tfe=data.get('dry_tfe', data.get('tfe', 0)),
                cao=data.get('dry_cao', data.get('cao', 0)),
                sio2=data.get('dry_sio2', data.get('sio2', 0)),
                mgo=data.get('dry_mgo', data.get('mgo', 0)),
                al2o3=data.get('dry_al2o3', data.get('al2o3', 0)),
                h2o=data.get('h2o', 0),
                ig=data.get('ig', 0),
                price=data.get('price', 0),
                min_ratio=data.get('min_ratio', 0),
                max_ratio=data.get('max_ratio', 100)
            )
            self.materials.append(material)

    def convert_wet_to_dry_components(self, wet_components: Dict, h2o: float) -> Dict:
        """湿基成分转干基成分"""
        dry_factor = 1 / (1 - h2o / 100.0)
        return {key: value * dry_factor for key, value in wet_components.items()}

    def calculate_sinter_properties(self, ratios: np.ndarray) -> Tuple[float, float, float, float, float]:
        """
        计算烧结矿性质 - 严格按照Excel表格公式实现
        Excel公式参考:
        - 湿配比: K4 (输入参数，百分比)
        - 干配比: L4 = K4*(1-J4/100)  (J4为水分)
        - 烧成量: M4 = L4*(1-I4/100)  (I4为烧损)
        - TFe: N4 = L4*B4/100
        - CaO: O4 = L4*C4/100
        - SiO2: P4 = L4*D4/100
        - MgO: Q4 = L4*E4/100
        - Al2O3: R4 = L4*F4/100
        - 吨矿单耗: S4 = L4*1000/0.99/M19 (M19为干配比总和)
        - 单位成本: T4 = S4*price/1000
        - 合计成本: U4 = SUM(T4:T17)
        """
        if len(ratios) != len(self.materials):
            raise ValueError("配比数组长度与原料数量不匹配")

        # 湿配比转换为小数形式 (K4列)
        wet_ratios = ratios / 100.0

        # 初始化累计变量
        total_dry_mass = 0.0  # 干配比总和 (M19)
        total_burnt_mass = 0.0  # 烧成量总和
        component_masses = {'TFe': 0, 'CaO': 0, 'SiO2': 0, 'MgO': 0, 'Al2O3': 0}
        total_cost = 0.0  # 合计成本 (U19)

        # 第一遍循环：计算各物料的干配比和烧成量
        dry_ratios = []
        burnt_masses = []

        for i, material in enumerate(self.materials):
            if wet_ratios[i] > 1e-6:  # 只计算有意义的配比
                # L4: 干配比 = K4*(1-J4/100)
                dry_ratio = wet_ratios[i] * (1 - material.h2o / 100.0)
                # M4: 烧成量 = L4*(1-I4/100)
                burnt_mass = dry_ratio * (1 - material.ig / 100.0)
            else:
                dry_ratio = 0.0
                burnt_mass = 0.0

            dry_ratios.append(dry_ratio)
            burnt_masses.append(burnt_mass)
            total_dry_mass += dry_ratio
            total_burnt_mass += burnt_mass

        # 检查总干配比和总烧成量
        if total_dry_mass < 1e-6 or total_burnt_mass < 1e-6:
            return 0, 0, 0, 0, 0

        # 第二遍循环：计算各成分和成本
        for i, material in enumerate(self.materials):
            if dry_ratios[i] > 1e-6:
                # 计算各成分质量 (基于干配比)
                # N4: TFe = L4*B4/100
                tfe_mass = dry_ratios[i] * material.tfe / 100.0
                # O4: CaO = L4*C4/100
                cao_mass = dry_ratios[i] * material.cao / 100.0
                # P4: SiO2 = L4*D4/100
                sio2_mass = dry_ratios[i] * material.sio2 / 100.0
                # Q4: MgO = L4*E4/100
                mgo_mass = dry_ratios[i] * material.mgo / 100.0
                # R4: Al2O3 = L4*F4/100
                al2o3_mass = dry_ratios[i] * material.al2o3 / 100.0

                # 累计各成分
                component_masses['TFe'] += tfe_mass
                component_masses['CaO'] += cao_mass
                component_masses['SiO2'] += sio2_mass
                component_masses['MgO'] += mgo_mass
                component_masses['Al2O3'] += al2o3_mass

                # S4: 吨矿单耗 = L4*1000/0.99/M19
                ton_consumption = dry_ratios[i] * 1000 / 0.99 / total_dry_mass

                # T4: 单位成本 = S4*price/1000
                unit_cost_contribution = ton_consumption * material.price / 1000
                total_cost += unit_cost_contribution

        # 计算最终烧结矿成分百分比
        # 烧结矿各成分含量 = 各成分总和 / 总烧成量 × 100%
        sinter_tfe = component_masses['TFe'] / total_burnt_mass * 100.0
        sinter_cao = component_masses['CaO'] / total_burnt_mass * 100.0
        sinter_sio2 = component_masses['SiO2'] / total_burnt_mass * 100.0
        sinter_mgo = component_masses['MgO'] / total_burnt_mass * 100.0
        sinter_al2o3 = component_masses['Al2O3'] / total_burnt_mass * 100.0

        # 计算碱度 R = CaO/SiO2
        sinter_r = sinter_cao / sinter_sio2 if sinter_sio2 > 1e-6 else 999

        # 最终单位成本就是total_cost (已经是元/吨)
        final_unit_cost = total_cost

        return sinter_tfe, sinter_r, sinter_mgo, sinter_al2o3, final_unit_cost

    def create_objective_function(self, optimization_type: OptimizationType):
        """创建目标函数"""
        def objective_function(ratios: np.ndarray) -> float:
            try:
                props = self.calculate_sinter_properties(ratios)
                if props[0] == 0:  # 计算失败
                    return 1e12

                sinter_tfe, sinter_r, sinter_mgo, sinter_al2o3, unit_cost = props

                if optimization_type == OptimizationType.COST_OPTIMAL:
                    # 成本最优：以最大程度降低成本为目标
                    # 成本权重较高，质量偏差惩罚较轻，R值允许更自由变化
                    cost_penalty = unit_cost / 100.0  # 成本权重

                    # 质量偏差惩罚较轻，允许在约束范围内有更大的灵活性
                    # 特别是R值的权重降低，允许更自由变化
                    quality_penalty = (
                        0.1 * (sinter_tfe - self.targets.tfe)**2 +
                        0.02 * (sinter_r - self.targets.r)**2 +  # R值权重降低，允许更自由变化
                        0.05 * (sinter_mgo - self.targets.mgo)**2 +
                        0.05 * (sinter_al2o3 - self.targets.al2o3)**2
                    )

                    # 成本超过680元/吨时增加重惩罚，但允许一定范围内的成本
                    if unit_cost > 680:
                        cost_penalty += 5.0 * (unit_cost - 680) / 100.0

                    return cost_penalty + quality_penalty

                else:  # QUALITY_OPTIMAL
                    # 质量最优：以质量指标贴近目标值为主要目标，成本控制为次要目标
                    # 质量权重较高，成本权重较低，特别强调R值接近1.9
                    quality_penalty = (
                        1.0 * (sinter_tfe - self.targets.tfe)**2 +
                        2.0 * (sinter_r - self.targets.r)**2 +  # R值权重增加，强制接近1.9
                        0.3 * (sinter_mgo - self.targets.mgo)**2 +
                        0.3 * (sinter_al2o3 - self.targets.al2o3)**2
                    )

                    # 成本约束：超过680元/吨时重惩罚，但在680以下不刻意追求最低
                    cost_penalty = 0
                    if unit_cost > 680:
                        cost_penalty = 100.0 * (unit_cost - 680) / 100.0
                    # 如果成本远低于目标值，也适当增加惩罚，避免过度降低成本而影响质量
                    elif unit_cost < 300:  # 成本过低可能意味着质量不佳
                        cost_penalty = 0.1 * (300 - unit_cost) / 100.0

                    return quality_penalty + cost_penalty

            except Exception:
                return 1e12

        return objective_function

    def create_constraints(self, optimization_type: OptimizationType):
        """创建约束条件"""
        constraints = []

        # 配比总和约束
        constraints.append({
            'type': 'eq',
            'fun': lambda x: np.sum(x) - 100.0
        })

        # 成分约束
        def create_component_constraint(component_idx, bound_type, bound_value):
            def constraint_func(x):
                try:
                    props = self.calculate_sinter_properties(x)
                    if props[0] == 0:
                        return -1e6 if bound_type == 'min' else 1e6
                    value = props[component_idx]

                    # 添加数值稳定性检查
                    if not np.isfinite(value):
                        return -1e6 if bound_type == 'min' else 1e6

                    if bound_type == 'min':
                        return value - bound_value
                    else:
                        return bound_value - value
                except Exception:
                    return -1e6 if bound_type == 'min' else 1e6
            return constraint_func

        # 根据优化类型设置不同的约束策略
        if optimization_type == OptimizationType.QUALITY_OPTIMAL:
            # 质量最优：严格按照约束条件范围
            # TFe约束 - 严格约束
            if self.constraints.tfe[0] > 0 and self.constraints.tfe[1] > self.constraints.tfe[0]:
                constraints.append({
                    'type': 'ineq',
                    'fun': create_component_constraint(0, 'min', self.constraints.tfe[0])
                })
                constraints.append({
                    'type': 'ineq',
                    'fun': create_component_constraint(0, 'max', self.constraints.tfe[1])
                })

            # 碱度约束 - 严格约束
            if self.constraints.r[0] > 0 and self.constraints.r[1] > self.constraints.r[0]:
                constraints.append({
                    'type': 'ineq',
                    'fun': create_component_constraint(1, 'min', self.constraints.r[0])
                })
                constraints.append({
                    'type': 'ineq',
                    'fun': create_component_constraint(1, 'max', self.constraints.r[1])
                })

            # MgO约束 - 严格约束
            if self.constraints.mgo[0] >= 0 and self.constraints.mgo[1] > self.constraints.mgo[0]:
                constraints.append({
                    'type': 'ineq',
                    'fun': create_component_constraint(2, 'min', self.constraints.mgo[0])
                })
                constraints.append({
                    'type': 'ineq',
                    'fun': create_component_constraint(2, 'max', self.constraints.mgo[1])
                })

            # Al2O3约束 - 严格约束
            if self.constraints.al2o3[0] >= 0 and self.constraints.al2o3[1] > self.constraints.al2o3[0]:
                constraints.append({
                    'type': 'ineq',
                    'fun': create_component_constraint(3, 'min', self.constraints.al2o3[0])
                })
                constraints.append({
                    'type': 'ineq',
                    'fun': create_component_constraint(3, 'max', self.constraints.al2o3[1])
                })

        else:  # COST_OPTIMAL
            # 成本最优：放宽部分约束条件的严格范围限制，以最大程度降低成本为目标
            # TFe约束 - 适当放宽范围
            tfe_tolerance = 0.5  # 允许0.5%的偏差
            if self.constraints.tfe[0] > 0 and self.constraints.tfe[1] > self.constraints.tfe[0]:
                constraints.append({
                    'type': 'ineq',
                    'fun': create_component_constraint(0, 'min', max(0, self.constraints.tfe[0] - tfe_tolerance))
                })
                constraints.append({
                    'type': 'ineq',
                    'fun': create_component_constraint(0, 'max', min(100, self.constraints.tfe[1] + tfe_tolerance))
                })

            # 碱度约束 - 适当放宽范围
            r_tolerance = 0.1  # 允许0.1的偏差
            if self.constraints.r[0] > 0 and self.constraints.r[1] > self.constraints.r[0]:
                constraints.append({
                    'type': 'ineq',
                    'fun': create_component_constraint(1, 'min', max(0, self.constraints.r[0] - r_tolerance))
                })
                constraints.append({
                    'type': 'ineq',
                    'fun': create_component_constraint(1, 'max', self.constraints.r[1] + r_tolerance)
                })

            # MgO约束 - 适当放宽范围
            mgo_tolerance = 0.5  # 允许0.5%的偏差
            if self.constraints.mgo[0] >= 0 and self.constraints.mgo[1] > self.constraints.mgo[0]:
                constraints.append({
                    'type': 'ineq',
                    'fun': create_component_constraint(2, 'min', max(0, self.constraints.mgo[0] - mgo_tolerance))
                })
                constraints.append({
                    'type': 'ineq',
                    'fun': create_component_constraint(2, 'max', self.constraints.mgo[1] + mgo_tolerance)
                })

            # Al2O3约束 - 适当放宽范围
            al2o3_tolerance = 0.2
            if self.constraints.al2o3[0] >= 0 and self.constraints.al2o3[1] > self.constraints.al2o3[0]:
                constraints.append({
                    'type': 'ineq',
                    'fun': create_component_constraint(3, 'min', max(0, self.constraints.al2o3[0] - al2o3_tolerance))
                })
                constraints.append({
                    'type': 'ineq',
                    'fun': create_component_constraint(3, 'max', self.constraints.al2o3[1] + al2o3_tolerance)
                })

        # 成本约束 - 添加合理性检查
        if self.constraints.cost[0] >= 0 and self.constraints.cost[1] > self.constraints.cost[0]:
            constraints.append({
                'type': 'ineq',
                'fun': create_component_constraint(4, 'min', self.constraints.cost[0])
            })
            constraints.append({
                'type': 'ineq',
                'fun': create_component_constraint(4, 'max', self.constraints.cost[1])
            })

        return constraints

    def _generate_initial_solution(self, bounds: List[Tuple[float, float]]) -> np.ndarray:
        """生成智能初始解 - 使用物料配比.md中的初始配比值"""
        n_materials = len(self.materials)
        x0 = np.zeros(n_materials)

        # 首先尝试使用预设的初始配比值
        total_initial = 0.0
        for i, material in enumerate(self.materials):
            # 从DEFAULT_MATERIALS中获取initial_ratio
            initial_ratio = 0.0
            for default_material in DEFAULT_MATERIALS:
                if default_material['name'] == material.name:
                    initial_ratio = default_material.get('initial_ratio', 0.0)
                    break
            
            if initial_ratio > 0 and bounds[i][1] > 0:
                # 确保初始配比在边界范围内
                x0[i] = max(bounds[i][0], min(bounds[i][1], initial_ratio))
                total_initial += x0[i]

        # 如果预设配比总和不为100%，需要调整
        if abs(total_initial - 100.0) > 1e-6:
            if total_initial > 0:
                # 按比例调整到100%
                scale_factor = 100.0 / total_initial
                for i in range(n_materials):
                    if x0[i] > 0:
                        x0[i] *= scale_factor
                        # 重新检查边界约束
                        x0[i] = max(bounds[i][0], min(bounds[i][1], x0[i]))
            else:
                # 如果没有预设配比，使用智能分配
                # 按TFe含量排序，优先选择高品位原料
                material_indices = list(range(n_materials))
                material_indices.sort(key=lambda i: self.materials[i].tfe, reverse=True)

                # 分配初始配比
                remaining_ratio = 100.0
                for i in material_indices:
                    material = self.materials[i]
                    if material.max_ratio > 0 and remaining_ratio > 0:
                        # 根据TFe含量和价格确定初始配比
                        if material.tfe > 50:  # 高品位原料
                            target_ratio = min(material.max_ratio, remaining_ratio * 0.6)
                        elif material.tfe > 30:  # 中品位原料
                            target_ratio = min(material.max_ratio, remaining_ratio * 0.3)
                        else:  # 低品位原料或熔剂
                            target_ratio = min(material.max_ratio, remaining_ratio * 0.1)

                        x0[i] = max(material.min_ratio, target_ratio)
                        remaining_ratio -= x0[i]

        # 最终归一化到100%
        total = np.sum(x0)
        if total > 0:
            x0 = x0 / total * 100.0

        # 确保满足边界约束
        for i in range(n_materials):
            x0[i] = np.clip(x0[i], bounds[i][0], bounds[i][1])

        return x0

    def _generate_fallback_solution(self, bounds: List[Tuple[float, float]]) -> np.ndarray:
        """生成备用初始解"""
        n_materials = len(self.materials)
        x0 = np.zeros(n_materials)

        # 简单平均分配
        available_materials = [i for i in range(n_materials) if bounds[i][1] > 0]
        if available_materials:
            ratio_per_material = 100.0 / len(available_materials)
            for i in available_materials:
                x0[i] = min(bounds[i][1], max(bounds[i][0], ratio_per_material))

        # 归一化
        total = np.sum(x0)
        if total > 0:
            x0 = x0 / total * 100.0

        return x0

    def optimize(self, optimization_type: OptimizationType, max_iterations: int = 2) -> OptimizationResult:
        """执行SQP优化算法"""
        print(f"\n{'='*60}")
        print(f"🔥 开始{optimization_type.value}优化")
        print(f"{'='*60}")

        # 创建目标函数和约束
        objective_func = self.create_objective_function(optimization_type)
        constraints = self.create_constraints(optimization_type)

        # 设置初始值和边界
        n_materials = len(self.materials)
        bounds = []

        # 根据原料设置边界
        for i, material in enumerate(self.materials):
            bounds.append((material.min_ratio, material.max_ratio))

        # 智能初始值设置
        x0 = self._generate_initial_solution(bounds)

        print(f"📋 初始配比: {[f'{self.materials[i].name}: {x0[i]:.2f}%' for i in range(n_materials) if x0[i] > 0.1]}")

        # 验证初始解的可行性
        initial_props = self.calculate_sinter_properties(x0)
        if initial_props[0] > 0:
            print(f"🎯 初始解性质: TFe={initial_props[0]:.2f}%, R={initial_props[1]:.2f}, 成本={initial_props[4]:.2f}元/吨")
        else:
            print("⚠️ 初始解计算失败，使用备用初始化方案")
            x0 = self._generate_fallback_solution(bounds)

        self.optimization_history = []
        best_result = None

        # 优化只进行两轮
        for iteration in range(min(max_iterations, 2)):
            try:
                print(f"📊 第 {iteration+1} 轮优化迭代...")

                # 执行SQP优化 - 使用更稳健的参数
                result = minimize(
                    objective_func,
                    x0,
                    method='SLSQP',
                    bounds=bounds,
                    constraints=constraints,
                    options={
                        'maxiter': 1000,
                        'ftol': 1e-6,
                        'eps': 1e-8,
                        'disp': False,
                        'finite_diff_rel_step': 1e-6
                    }
                )

                if result.success:
                    # 验证结果
                    props = self.calculate_sinter_properties(result.x)
                    if props[0] > 0:  # 计算成功
                        ratios_dict = {}
                        wet_ratios_dict = {}  # 添加湿配比字典
                        for i, material in enumerate(self.materials):
                            if result.x[i] > 0.01:  # 只包含有意义的配比
                                # 干基配比
                                ratios_dict[material.name] = float(result.x[i])
                                # 湿基配比 = 干基配比 / (1 - 水分/100)
                                wet_ratio = result.x[i] / (1 - material.h2o / 100.0) if material.h2o < 100 else 0
                                wet_ratios_dict[material.name] = float(wet_ratio)

                        properties = {
                            'TFe': float(props[0]),
                            'R': float(props[1]),
                            'MgO': float(props[2]),
                            'Al2O3': float(props[3]),
                            'Cost': float(props[4]),
                            'cao': float(props[1] * props[3]),  # 计算CaO含量
                            'sio2': float(props[3]),  # SiO2含量
                            'tio2': 0.0  # 默认TiO2含量
                        }

                        # 根据优化类型设置不同的消息
                        optimization_type_text = "成本最优" if optimization_type == OptimizationType.COST_OPTIMAL else "质量最优"
                        success_message = f"{optimization_type_text}优化成功"

                        best_result = OptimizationResult(
                            success=True,
                            ratios=ratios_dict,
                            properties=properties,
                            cost=float(props[4]),
                            objective_value=float(result.fun),
                            iterations=iteration + 1,
                            message=success_message
                        )

                        print(f"✅ 第 {iteration+1} 轮{optimization_type_text}优化成功，目标函数值: {result.fun:.6f}")
                        print(f"🎯 TFe: {props[0]:.2f}%, R: {props[1]:.2f}, 成本: {props[4]:.2f}元/吨")
                        
                        # 输出详细的湿配比信息
                        print("📊 湿配比详情:")
                        for name, wet_ratio in wet_ratios_dict.items():
                            if wet_ratio > 0.01:
                                dry_ratio = ratios_dict.get(name, 0)
                                print(f"   - {name}: 湿配比 {wet_ratio:.2f}%, 干配比 {dry_ratio:.2f}%")

                        # 输出优化策略信息
                        if optimization_type == OptimizationType.COST_OPTIMAL:
                            print(f"💰 成本最优策略: 在适当放宽约束条件下，以最大程度降低成本为目标")
                        else:
                            print(f"🎯 质量最优策略: 严格按照约束条件范围，在成本上限680元/吨内寻找最优质量解")

                        # 只有在第一轮结束后才进行第二轮优化
                        if iteration == 0:
                            # 检查是否需要调整低配比原料
                            low_ratio_indices = [i for i, ratio in enumerate(result.x)
                                               if 0 < ratio < 2.0 and bounds[i][1] > 0]

                            if not low_ratio_indices:
                                print(f"🎉 优化收敛成功！")
                                break

                            # 调整低配比原料
                            for idx in low_ratio_indices:
                                bounds[idx] = (0, 0)
                                x0[idx] = 0
                                print(f"   - {self.materials[idx].name}: {result.x[idx]:.3f}% -> 0%")

                            # 重新归一化
                            remaining_sum = sum(x0[i] for i in range(n_materials) if bounds[i][1] > 0)
                            if remaining_sum > 0:
                                for i in range(n_materials):
                                    if bounds[i][1] > 0:
                                        x0[i] = x0[i] / remaining_sum * 100.0
                        else:
                            print(f"🎉 优化完成！")
                            break
                else:
                    print(f"❌ 第 {iteration+1} 轮优化失败: {result.message}")
                    if iteration == 0:
                        # 添加随机扰动重试
                        x0 += np.random.uniform(-5, 5, n_materials)
                        x0 = np.clip(x0, [b[0] for b in bounds], [b[1] for b in bounds])
                    else:
                        break

            except Exception as e:
                print(f"💥 优化迭代 {iteration+1} 异常: {e}")
                break

        if best_result is None:
            return OptimizationResult(
                success=False,
                ratios={},
                properties={},
                cost=0,
                objective_value=0,
                iterations=max_iterations,
                message="优化失败，未找到可行解"
            )

        return best_result

# ======================== 工具函数 ========================

def convert_numpy_types(obj):
    """将NumPy类型转换为Python原生类型"""
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, (np.bool_, bool)):
        return bool(obj)
    elif isinstance(obj, dict):
        return {key: convert_numpy_types(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(item) for item in obj]
    return obj

# ======================== Flask应用初始化 ========================

app = Flask(__name__)
app.secret_key = 'sinter-optimization-service'

# CORS设置
if CORS_AVAILABLE:
    CORS(app, origins=['http://localhost:8080', 'http://localhost:3000', 'http://127.0.0.1:3000', 'http://localhost:5000'])
else:
    @app.after_request
    def after_request(response):
        origin = request.headers.get('Origin')
        allowed_origins = ['http://localhost:8080', 'http://localhost:3000', 'http://127.0.0.1:3000']
        if origin in allowed_origins:
            response.headers.add('Access-Control-Allow-Origin', origin)
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
        response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
        return response

# 全局优化器实例
optimizer = SinterOptimizer()

# ======================== 默认数据配置 ========================

# 默认原料数据 - 根据物料配比.md更新初始配比值
DEFAULT_MATERIALS = [
    {"name": "碱性精粉", "tfe": 63.76, "cao": 1.94, "sio2": 4.95, "mgo": 1.85, "al2o3": 0.60, "h2o": 8.20, "ig": 1.23, "price": 752.21, "min_ratio": 0, "max_ratio": 30, "initial_ratio": 0.0},
    {"name": "酸性精粉", "tfe": 64.89, "cao": 0.70, "sio2": 6.32, "mgo": 0.92, "al2o3": 0.72, "h2o": 9.90, "ig": -0.05, "price": 752.21, "min_ratio": 0, "max_ratio": 30, "initial_ratio": 0.0},
    {"name": "海瑞", "tfe": 58.07, "cao": 0.10, "sio2": 6.21, "mgo": 0.28, "al2o3": 2.52, "h2o": 6.00, "ig": 9.07, "price": 822.98, "min_ratio": 0, "max_ratio": 25, "initial_ratio": 0.0},
    {"name": "印粉海娜", "tfe": 63.66, "cao": 0.10, "sio2": 4.01, "mgo": 0.24, "al2o3": 2.42, "h2o": 6.70, "ig": 1.60, "price": 832.98, "min_ratio": 15, "max_ratio": 25, "initial_ratio": 21.0},
    {"name": "巴西粗粉", "tfe": 64.64, "cao": 0.20, "sio2": 4.69, "mgo": 0.11, "al2o3": 0.73, "h2o": 6.70, "ig": 1.33, "price": 1473.05, "min_ratio": 0, "max_ratio": 20, "initial_ratio": 0.0},
    {"name": "俄罗斯精粉", "tfe": 62.95, "cao": 1.71, "sio2": 4.61, "mgo": 3.70, "al2o3": 2.29, "h2o": 10.00, "ig": -0.35, "price": 772.21, "min_ratio": 15, "max_ratio": 25, "initial_ratio": 20.0},
    {"name": "高炉返矿", "tfe": 55.54, "cao": 10.60, "sio2": 5.59, "mgo": 2.34, "al2o3": 2.09, "h2o": 0.50, "ig": 1.73, "price": 550.00, "min_ratio": 20, "max_ratio": 30, "initial_ratio": 25.0},
    {"name": "回收料", "tfe": 56.16, "cao": 6.56, "sio2": 6.31, "mgo": 2.39, "al2o3": 2.51, "h2o": 10.73, "ig": 1.74, "price": 100.00, "min_ratio": 5, "max_ratio": 10, "initial_ratio": 7.0},
    {"name": "钢渣", "tfe": 26.46, "cao": 28.15, "sio2": 15.43, "mgo": 2.79, "al2o3": 2.53, "h2o": 7.60, "ig": 12.05, "price": 550.00, "min_ratio": 2, "max_ratio": 6, "initial_ratio": 4.0},
    {"name": "氧化铁皮", "tfe": 69.73, "cao": 0.50, "sio2": 1.50, "mgo": 0.00, "al2o3": 2.88, "h2o": 5.90, "ig": -1.52, "price": 750.00, "min_ratio": 0, "max_ratio": 8, "initial_ratio": 0.0},
    {"name": "生石灰", "tfe": 0.00, "cao": 71.74, "sio2": 3.52, "mgo": 2.28, "al2o3": 1.19, "h2o": 7.00, "ig": 16.33, "price": 219.00, "min_ratio": 5, "max_ratio": 8, "initial_ratio": 6.0},
    {"name": "轻烧白云石", "tfe": 0.00, "cao": 42.67, "sio2": 5.31, "mgo": 26.12, "al2o3": 0.10, "h2o": 1.50, "ig": 19.73, "price": 183.76, "min_ratio": 2, "max_ratio": 5, "initial_ratio": 4.0},
    {"name": "焦粉", "tfe": 0.19, "cao": 0.37, "sio2": 8.82, "mgo": 0.22, "al2o3": 3.31, "h2o": 13.15, "ig": 79.40, "price": 520.00, "min_ratio": 3, "max_ratio": 5, "initial_ratio": 4.0},
    {"name": "澳粉纵横", "tfe": 60.80, "cao": 0.10, "sio2": 4.35, "mgo": 0.20, "al2o3": 2.30, "h2o": 8.30, "ig": 6.89, "price": 832.98, "min_ratio": 8, "max_ratio": 15, "initial_ratio": 9.0}
]

# 初始化优化器
def initialize_optimizer():
    """初始化优化器"""
    optimizer.load_materials(DEFAULT_MATERIALS)
    optimizer.targets = OptimizationTarget(tfe=55.0, r=1.90, mgo=2.0, al2o3=1.8)
    # 为不同的优化类型设置不同的默认约束
    optimizer.constraints = ConstraintRanges(
        tfe=(54, 55),
        r=(1.88, 2.02),
        mgo=(1.6, 2.8),
        al2o3=(1.6, 2.0),
        cost=(0, 680)
    )

# 初始化
initialize_optimizer()

# ======================== 简化的API接口 ========================

@app.route('/')
def index():
    """主页"""
    return jsonify({
        'message': '烧结配料计算模型系统',
        'version': '2.0.0',
        'status': 'running',
        'endpoints': {
            'solve': '/api/solve',
            'health': '/health'
        },
        'output_example': {
            'cost_optimal': {
                '方案': '各种物料配比和为1的配比方案',
                '湿配比（%）': [8.5, 15.2, 20.1, 18.7, 12.3, 5.8, 2.4, 17.0],
                '成本（元/吨）': 320.5,
                'TFe（%）': 54.2,
                'R': 1.78
            },
            'quality_optimal': {
                '方案': '各种物料配比和为1的配比方案',
                '湿配比（%）': [9.2, 14.8, 19.5, 19.1, 11.9, 6.2, 2.1, 17.2],
                'TFe偏差(%)': '+0.1',
                'R偏差(%)': '-0.05',
                'MgO（%）': 2.3
            }
        }
    })

@app.route('/health')
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.datetime.now().isoformat(),
        'service': 'SQP优化服务'
    })

@app.route('/api/solve', methods=['POST'])
def solve_optimization():
    """
    SQP优化求解接口
    支持成本最优和质量最优双目标优化
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': '请求数据为空'}), 400

        # 更新原料数据
        if 'raw_materials' in data:
            optimizer.load_materials(data['raw_materials'])

        # 更新目标参数
        if 'target' in data:
            target_data = data['target']
            optimizer.targets = OptimizationTarget(
                tfe=target_data.get('tfe_target', 55.0),
                r=target_data.get('ro_target', 1.90),
                mgo=target_data.get('mgo_target', 2.0),
                al2o3=target_data.get('al2o3_target', 1.8)
            )

        # 更新约束条件
        if 'constraints' in data:
            constraints_data = data['constraints']
            optimizer.constraints = ConstraintRanges(
                tfe=constraints_data.get('tfe_range', (54, 55)),
                r=constraints_data.get('ro_range', (1.88, 2.02)),
                mgo=constraints_data.get('mgo_range', (1.6, 2.8)),
                al2o3=constraints_data.get('al2o3_range', (1.6, 2.0)),
                cost=constraints_data.get('cost_range', (0, 680))
            )

        # 确定优化类型
        optimize_type = data.get('optimize_type', 'cost')
        optimization_type = OptimizationType.COST_OPTIMAL if optimize_type == 'cost' else OptimizationType.QUALITY_OPTIMAL

        print(f"\n🔥 开始{optimization_type.value}优化")
        print(f"目标: TFe={optimizer.targets.tfe}, R={optimizer.targets.r}")

        # 执行优化
        result = optimizer.optimize(optimization_type)

        if result.success:
            # 构建返回结果
            optimization_type_text = "成本最优" if optimization_type == OptimizationType.COST_OPTIMAL else "质量最优"
            
            # 计算湿配比
            wet_ratios = {}
            for name, dry_ratio in result.ratios.items():
                # 查找对应原料的水分含量
                for material in optimizer.materials:
                    if material.name == name:
                        # 湿基配比 = 干基配比 / (1 - 水分/100)
                        wet_ratio = dry_ratio / (1 - material.h2o / 100.0) if material.h2o < 100 else 0
                        wet_ratios[name] = wet_ratio
                        break

            response = {
                'success': True,
                'optimization_type': optimize_type,
                'optimization_type_text': optimization_type_text,
                'optimal_ratios': result.ratios,
                'wet_ratios': wet_ratios,  # 添加湿配比
                'sinter_properties': {
                    'tfe': result.properties.get('TFe', 0),
                    'r': result.properties.get('R', 0),
                    'mgo': result.properties.get('MgO', 0),
                    'al2o3': result.properties.get('Al2O3', 0),
                    'cost': result.properties.get('Cost', 0),
                    'cao': result.properties.get('cao', 0),
                    'sio2': result.properties.get('sio2', 0),
                    'tio2': result.properties.get('tio2', 0)
                },
                'unit_cost': result.cost,
                'objective_value': result.objective_value,
                'iterations': result.iterations,
                'message': result.message,
                'algorithm_strategy': "在适当放宽约束条件下，以最大程度降低成本为目标" if optimization_type == OptimizationType.COST_OPTIMAL else "严格按照约束条件范围，在成本上限680元/吨内寻找最优质量解",
                'timestamp': datetime.datetime.now().isoformat()
            }

            # 如果需要多方案对比，同时计算另一种优化类型
            if data.get('multi_solution', False):
                other_type = OptimizationType.QUALITY_OPTIMAL if optimization_type == OptimizationType.COST_OPTIMAL else OptimizationType.COST_OPTIMAL
                other_type_text = "质量最优" if other_type == OptimizationType.QUALITY_OPTIMAL else "成本最优"

                print(f"\n🔄 开始计算{other_type_text}方案对比...")
                other_result = optimizer.optimize(other_type)
                
                # 计算湿配比
                other_wet_ratios = {}
                for name, dry_ratio in other_result.ratios.items():
                    # 查找对应原料的水分含量
                    for material in optimizer.materials:
                        if material.name == name:
                            # 湿基配比 = 干基配比 / (1 - 水分/100)
                            wet_ratio = dry_ratio / (1 - material.h2o / 100.0) if material.h2o < 100 else 0
                            other_wet_ratios[name] = wet_ratio
                            break

                if other_result.success:
                    response['alternative_solution'] = {
                        'optimization_type': other_type.value,
                        'optimization_type_text': other_type_text,
                        'optimal_ratios': other_result.ratios,
                        'wet_ratios': other_wet_ratios,  # 添加湿配比
                        'sinter_properties': {
                            'tfe': other_result.properties.get('TFe', 0),
                            'r': other_result.properties.get('R', 0),
                            'mgo': other_result.properties.get('MgO', 0),
                            'al2o3': other_result.properties.get('Al2O3', 0),
                            'cost': other_result.properties.get('Cost', 0),
                            'cao': other_result.properties.get('cao', 0),
                            'sio2': other_result.properties.get('sio2', 0),
                            'tio2': other_result.properties.get('tio2', 0)
                        },
                        'unit_cost': other_result.cost,
                        'objective_value': other_result.objective_value
                    }

            print(f"✅ 优化成功: 成本={result.cost:.2f}元/吨")
            return jsonify(response)
        else:
            return jsonify({
                'success': False,
                'error': result.message,
                'iterations': result.iterations
            }), 400

    except Exception as e:
        print(f"❌ 优化过程异常: {e}")
        return jsonify({
            'success': False,
            'error': f'服务器内部错误: {str(e)}'
        }), 500

if __name__ == '__main__':
    port = 5000
    print(f"🚀 启动烧结配料优化服务...")
    print(f"📡 服务地址: http://localhost:{port}")
    print(f"📝 API文档: http://localhost:{port}")
    app.run(host='0.0.0.0', port=port, debug=False)
