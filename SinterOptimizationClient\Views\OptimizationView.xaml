<UserControl x:Class="SinterOptimizationClient.Views.OptimizationView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:views="clr-namespace:SinterOptimizationClient.Views"
    xmlns:converters="clr-namespace:SinterOptimizationClient.Converters"
    mc:Ignorable="d" d:DesignHeight="900" d:DesignWidth="1600">

    <UserControl.Resources>
        <!-- 转换器 -->
        <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
        <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:EnumToBooleanConverter x:Key="EnumToBooleanConverter"/>
        
        <!-- 增强的启动算法计算按钮样式 -->
        <Style x:Key="EnhancedCalculationButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#F97316"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Height" Value="50"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="8" 
                                BorderThickness="0"
                                x:Name="ButtonBorder">
                            <Grid>
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                <!-- 加载指示器 -->
                                <ProgressBar x:Name="LoadingIndicator" 
                                           IsIndeterminate="True" 
                                           Height="3" 
                                           VerticalAlignment="Bottom" 
                                           Margin="5,0,5,5"
                                           Visibility="Collapsed"
                                           Background="Transparent"
                                           Foreground="White"/>
                            </Grid>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#EA580C"/>
                                <Setter TargetName="ButtonBorder" Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="#F97316" BlurRadius="10" ShadowDepth="0" Opacity="0.5"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#DC2626"/>
                                <Setter TargetName="ButtonBorder" Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="#9CA3AF"/>
                                <Setter Property="Foreground" Value="#6B7280"/>
                                <Setter TargetName="LoadingIndicator" Property="Visibility" Value="Visible"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid Background="{StaticResource LightGrayBrush}">
        <Grid.RowDefinitions>
            <!-- 物料信息表 - 占50%高度 -->
            <RowDefinition Height="0.5*"/>
            <!-- 优化计算区 - 占50%高度 -->
            <RowDefinition Height="0.5*"/>
        </Grid.RowDefinitions>

        <!-- 物料信息表区域 -->
        <Border Grid.Row="0" Background="White" BorderBrush="{StaticResource BorderBrush}" BorderThickness="0,0,0,1">
            <views:MaterialDataView DataContext="{Binding MaterialDataViewModel}"/>
        </Border>

        <!-- 优化配置区 - 简化为6:4布局 -->
        <Grid Grid.Row="1" Background="White" Margin="10">
            <Grid.ColumnDefinitions>
                <!-- 左侧配置区 - 占60% -->
                <ColumnDefinition Width="0.6*"/>
                <!-- 右侧操作区 - 占40% -->
                <ColumnDefinition Width="0.4*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧：配置参数区 -->
            <Border Grid.Column="0" Background="#F8FAFC" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="8" Margin="0,0,10,0" Padding="15">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <!-- 成分约束配置区 -->
                            <ColumnDefinition Width="420"/>
                            <!-- 物料最初配比显示区 -->
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- 左侧：成分约束配置 -->
                        <StackPanel Grid.Column="0" Margin="0,0,15,0">
                            <TextBlock Text="成分约束配置" FontSize="16" FontWeight="Bold" Foreground="#1E3A8A" Margin="0,0,0,15"/>

                            <Grid Margin="0,0,0,20">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="80"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="35"/>
                                    <RowDefinition Height="35"/>
                                    <RowDefinition Height="35"/>
                                    <RowDefinition Height="35"/>
                                    <RowDefinition Height="35"/>
                                    <RowDefinition Height="35"/>
                                </Grid.RowDefinitions>

                                <!-- 表头 -->
                                <TextBlock Grid.Row="0" Grid.Column="0" Text="成分" HorizontalAlignment="Center" FontSize="13" FontWeight="Bold" Foreground="#374151"/>
                                <TextBlock Grid.Row="0" Grid.Column="1" Text="最小值" HorizontalAlignment="Center" FontSize="13" FontWeight="Bold" Foreground="#374151"/>
                                <TextBlock Grid.Row="0" Grid.Column="2" Text="最大值" HorizontalAlignment="Center" FontSize="13" FontWeight="Bold" Foreground="#374151"/>
                                <TextBlock Grid.Row="0" Grid.Column="3" Text="目标值" HorizontalAlignment="Center" FontSize="13" FontWeight="Bold" Foreground="#374151"/>
                                <TextBlock Grid.Row="0" Grid.Column="4" Text="偏差" HorizontalAlignment="Center" FontSize="13" FontWeight="Bold" Foreground="#374151"/>

                                <!-- TFe -->
                                <TextBlock Grid.Row="1" Grid.Column="0" Text="TFe(%)" VerticalAlignment="Center" FontSize="12" FontWeight="Medium" Foreground="#1F2937"/>
                                <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding Parameters.TfeMin, StringFormat=F1}" Height="28" FontSize="12" Padding="5" Margin="2" BorderBrush="#D1D5DB" BorderThickness="1"/>
                                <TextBox Grid.Row="1" Grid.Column="2" Text="{Binding Parameters.TfeMax, StringFormat=F1}" Height="28" FontSize="12" Padding="5" Margin="2" BorderBrush="#D1D5DB" BorderThickness="1"/>
                                <TextBox Grid.Row="1" Grid.Column="3" Text="{Binding Parameters.TfeTarget, StringFormat=F1}" Height="28" FontSize="12" Padding="5" Margin="2" BorderBrush="#D1D5DB" BorderThickness="1"/>
                                <TextBox Grid.Row="1" Grid.Column="4" Text="{Binding Parameters.TfeDeviation, StringFormat=F2}" Height="28" FontSize="12" Padding="5" Margin="2" BorderBrush="#D1D5DB" BorderThickness="1"/>

                                <!-- SiO2 -->
                                <TextBlock Grid.Row="2" Grid.Column="0" Text="SiO₂(%)" VerticalAlignment="Center" FontSize="12" FontWeight="Medium" Foreground="#1F2937"/>
                                <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding Parameters.Sio2Min, StringFormat=F1}" Height="28" FontSize="12" Padding="5" Margin="2" BorderBrush="#D1D5DB" BorderThickness="1"/>
                                <TextBox Grid.Row="2" Grid.Column="2" Text="{Binding Parameters.Sio2Max, StringFormat=F1}" Height="28" FontSize="12" Padding="5" Margin="2" BorderBrush="#D1D5DB" BorderThickness="1"/>
                                <TextBox Grid.Row="2" Grid.Column="3" Text="{Binding Parameters.Sio2Target, StringFormat=F1}" Height="28" FontSize="12" Padding="5" Margin="2" BorderBrush="#D1D5DB" BorderThickness="1"/>
                                <TextBox Grid.Row="2" Grid.Column="4" Text="{Binding Parameters.Sio2Deviation, StringFormat=F2}" Height="28" FontSize="12" Padding="5" Margin="2" BorderBrush="#D1D5DB" BorderThickness="1"/>

                                <!-- CaO -->
                                <TextBlock Grid.Row="3" Grid.Column="0" Text="CaO(%)" VerticalAlignment="Center" FontSize="12" FontWeight="Medium" Foreground="#1F2937"/>
                                <TextBox Grid.Row="3" Grid.Column="1" Text="{Binding Parameters.CaoMin, StringFormat=F1}" Height="28" FontSize="12" Padding="5" Margin="2" BorderBrush="#D1D5DB" BorderThickness="1"/>
                                <TextBox Grid.Row="3" Grid.Column="2" Text="{Binding Parameters.CaoMax, StringFormat=F1}" Height="28" FontSize="12" Padding="5" Margin="2" BorderBrush="#D1D5DB" BorderThickness="1"/>
                                <TextBox Grid.Row="3" Grid.Column="3" Text="{Binding Parameters.CaoTarget, StringFormat=F1}" Height="28" FontSize="12" Padding="5" Margin="2" BorderBrush="#D1D5DB" BorderThickness="1"/>
                                <TextBox Grid.Row="3" Grid.Column="4" Text="{Binding Parameters.CaoDeviation, StringFormat=F2}" Height="28" FontSize="12" Padding="5" Margin="2" BorderBrush="#D1D5DB" BorderThickness="1"/>

                                <!-- MgO -->
                                <TextBlock Grid.Row="4" Grid.Column="0" Text="MgO(%)" VerticalAlignment="Center" FontSize="12" FontWeight="Medium" Foreground="#1F2937"/>
                                <TextBox Grid.Row="4" Grid.Column="1" Text="{Binding Parameters.MgoMin, StringFormat=F1}" Height="28" FontSize="12" Padding="5" Margin="2" BorderBrush="#D1D5DB" BorderThickness="1"/>
                                <TextBox Grid.Row="4" Grid.Column="2" Text="{Binding Parameters.MgoMax, StringFormat=F1}" Height="28" FontSize="12" Padding="5" Margin="2" BorderBrush="#D1D5DB" BorderThickness="1"/>
                                <TextBox Grid.Row="4" Grid.Column="3" Text="{Binding Parameters.MgoTarget, StringFormat=F1}" Height="28" FontSize="12" Padding="5" Margin="2" BorderBrush="#D1D5DB" BorderThickness="1"/>
                                <TextBox Grid.Row="4" Grid.Column="4" Text="{Binding Parameters.MgoDeviation, StringFormat=F2}" Height="28" FontSize="12" Padding="5" Margin="2" BorderBrush="#D1D5DB" BorderThickness="1"/>

                                <!-- Al2O3 -->
                                <TextBlock Grid.Row="5" Grid.Column="0" Text="Al₂O₃(%)" VerticalAlignment="Center" FontSize="12" FontWeight="Medium" Foreground="#1F2937"/>
                                <TextBox Grid.Row="5" Grid.Column="1" Text="{Binding Parameters.Al2o3Min, StringFormat=F1}" Height="28" FontSize="12" Padding="5" Margin="2" BorderBrush="#D1D5DB" BorderThickness="1"/>
                                <TextBox Grid.Row="5" Grid.Column="2" Text="{Binding Parameters.Al2o3Max, StringFormat=F1}" Height="28" FontSize="12" Padding="5" Margin="2" BorderBrush="#D1D5DB" BorderThickness="1"/>
                                <TextBox Grid.Row="5" Grid.Column="3" Text="{Binding Parameters.Al2o3Target, StringFormat=F1}" Height="28" FontSize="12" Padding="5" Margin="2" BorderBrush="#D1D5DB" BorderThickness="1"/>
                                <TextBox Grid.Row="5" Grid.Column="4" Text="{Binding Parameters.Al2o3Deviation, StringFormat=F2}" Height="28" FontSize="12" Padding="5" Margin="2" BorderBrush="#D1D5DB" BorderThickness="1"/>
                            </Grid>

                            <!-- 成本与约束配置 -->
                            <TextBlock Text="成本与约束配置" FontSize="16" FontWeight="Bold" Foreground="#1E3A8A" Margin="0,20,0,15"/>

                            <!-- 碱度计算方式选择 -->
                            <TextBlock Text="碱度计算方式:" FontSize="13" FontWeight="Medium" Foreground="#374151" Margin="0,0,0,8"/>
                            <StackPanel Margin="0,0,0,15">
                                <RadioButton Content="Ro = CaO/SiO₂" FontSize="13" IsChecked="{Binding Parameters.RoCalculationType, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=CaoSio2}" Margin="0,0,0,8" Foreground="#374151"/>
                                <RadioButton Content="Ro = (CaO+MgO)/(SiO₂+Al₂O₃)" FontSize="13" IsChecked="{Binding Parameters.RoCalculationType, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=CaoMgoSio2Al2o3}" Foreground="#374151"/>
                            </StackPanel>

                            <!-- 碱度约束参数 -->
                            <TextBlock Text="碱度约束参数:" FontSize="13" FontWeight="Medium" Foreground="#374151" Margin="0,0,0,8"/>
                            <Grid Margin="0,0,0,20">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="80"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Column="0" Text="最小值" HorizontalAlignment="Center" FontSize="13" FontWeight="Bold" Foreground="#374151" Margin="0,0,0,8"/>
                                <TextBlock Grid.Column="1" Text="最大值" HorizontalAlignment="Center" FontSize="13" FontWeight="Bold" Foreground="#374151" Margin="0,0,0,8"/>
                                <TextBlock Grid.Column="2" Text="目标值" HorizontalAlignment="Center" FontSize="13" FontWeight="Bold" Foreground="#374151" Margin="0,0,0,8"/>
                                <TextBlock Grid.Column="3" Text="偏差" HorizontalAlignment="Center" FontSize="13" FontWeight="Bold" Foreground="#374151" Margin="0,0,0,8"/>

                                <TextBox Grid.Column="0" Text="{Binding Parameters.RoMin, StringFormat=F2}" Height="28" FontSize="12" Padding="5" Margin="2,8,2,0" BorderBrush="#D1D5DB" BorderThickness="1"/>
                                <TextBox Grid.Column="1" Text="{Binding Parameters.RoMax, StringFormat=F2}" Height="28" FontSize="12" Padding="5" Margin="2,8,2,0" BorderBrush="#D1D5DB" BorderThickness="1"/>
                                <TextBox Grid.Column="2" Text="{Binding Parameters.RoTarget, StringFormat=F2}" Height="28" FontSize="12" Padding="5" Margin="2,8,2,0" BorderBrush="#D1D5DB" BorderThickness="1"/>
                                <TextBox Grid.Column="3" Text="{Binding Parameters.RoDeviation, StringFormat=F3}" Height="28" FontSize="12" Padding="5" Margin="2,8,2,0" BorderBrush="#D1D5DB" BorderThickness="1"/>
                            </Grid>

                            <!-- 生产参数 -->
                            <TextBlock Text="生产参数:" FontSize="13" FontWeight="Medium" Foreground="#374151" Margin="0,0,0,8"/>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="100"/>
                                    <ColumnDefinition Width="40"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="35"/>
                                    <RowDefinition Height="35"/>
                                    <RowDefinition Height="35"/>
                                    <RowDefinition Height="35"/>
                                    <RowDefinition Height="35"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="计划日产:" VerticalAlignment="Center" FontSize="13" Foreground="#374151"/>
                                <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding Parameters.PlannedDailyOutput, StringFormat=F0}" Height="28" FontSize="12" Padding="5" Margin="2" BorderBrush="#D1D5DB" BorderThickness="1"/>
                                <TextBlock Grid.Row="0" Grid.Column="2" Text="吨" VerticalAlignment="Center" FontSize="12" Foreground="#6B7280" Margin="5,0,0,0"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="金属收得率:" VerticalAlignment="Center" FontSize="13" Foreground="#374151"/>
                                <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding Parameters.MetalRecoveryRate, StringFormat=F1}" Height="28" FontSize="12" Padding="5" Margin="2" BorderBrush="#D1D5DB" BorderThickness="1"/>
                                <TextBlock Grid.Row="1" Grid.Column="2" Text="%" VerticalAlignment="Center" FontSize="12" Foreground="#6B7280" Margin="5,0,0,0"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="出矿率:" VerticalAlignment="Center" FontSize="13" Foreground="#374151"/>
                                <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding Parameters.SinterYield, StringFormat=F1}" Height="28" FontSize="12" Padding="5" Margin="2" BorderBrush="#D1D5DB" BorderThickness="1"/>
                                <TextBlock Grid.Row="2" Grid.Column="2" Text="%" VerticalAlignment="Center" FontSize="12" Foreground="#6B7280" Margin="5,0,0,0"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="作业率:" VerticalAlignment="Center" FontSize="13" Foreground="#374151"/>
                                <TextBox Grid.Row="3" Grid.Column="1" Text="{Binding Parameters.OperationRate, StringFormat=F1}" Height="28" FontSize="12" Padding="5" Margin="2" BorderBrush="#D1D5DB" BorderThickness="1"/>
                                <TextBlock Grid.Row="3" Grid.Column="2" Text="%" VerticalAlignment="Center" FontSize="12" Foreground="#6B7280" Margin="5,0,0,0"/>

                                <TextBlock Grid.Row="4" Grid.Column="0" Text="成本上限:" VerticalAlignment="Center" FontSize="13" Foreground="#374151"/>
                                <TextBox Grid.Row="4" Grid.Column="1" Text="{Binding Parameters.CostTargetMax, StringFormat=F0}" Height="28" FontSize="12" Padding="5" Margin="2" BorderBrush="#D1D5DB" BorderThickness="1"/>
                                <TextBlock Grid.Row="4" Grid.Column="2" Text="元/吨" VerticalAlignment="Center" FontSize="12" Foreground="#6B7280" Margin="5,0,0,0"/>
                            </Grid>
                        </StackPanel>

                        <!-- 右侧：物料最初配比显示 -->
                        <StackPanel Grid.Column="1">
                            <TextBlock Text="物料最初配比" FontSize="16" FontWeight="Bold" Foreground="#1E3A8A" Margin="0,0,0,15"/>

                            <Border Background="White" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="6" Padding="10">
                                <ScrollViewer MaxHeight="300" VerticalScrollBarVisibility="Auto">
                                    <StackPanel>
                                        <!-- 配比表头 -->
                                        <Grid Margin="0,0,0,10">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="120"/>
                                                <ColumnDefinition Width="80"/>
                                                <ColumnDefinition Width="60"/>
                                                <ColumnDefinition Width="60"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Grid.Column="0" Text="物料名称" FontSize="12" FontWeight="Bold" Foreground="#374151"/>
                                            <TextBlock Grid.Column="1" Text="初始配比" FontSize="12" FontWeight="Bold" Foreground="#374151"/>
                                            <TextBlock Grid.Column="2" Text="最小值" FontSize="12" FontWeight="Bold" Foreground="#374151"/>
                                            <TextBlock Grid.Column="3" Text="最大值" FontSize="12" FontWeight="Bold" Foreground="#374151"/>
                                        </Grid>

                                        <!-- 物料配比列表 -->
                                        <ItemsControl ItemsSource="{Binding MaterialDataViewModel.Materials}">
                                            <ItemsControl.ItemTemplate>
                                                <DataTemplate>
                                                    <Grid Margin="0,2">
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="120"/>
                                                            <ColumnDefinition Width="80"/>
                                                            <ColumnDefinition Width="60"/>
                                                            <ColumnDefinition Width="60"/>
                                                        </Grid.ColumnDefinitions>
                                                        <TextBlock Grid.Column="0" Text="{Binding Name}" FontSize="11" VerticalAlignment="Center" Foreground="#1F2937"/>
                                                        <TextBlock Grid.Column="1" Text="{Binding InitialRatio, StringFormat=F1}" FontSize="11" VerticalAlignment="Center" Foreground="#F97316" FontWeight="Medium"/>
                                                        <TextBlock Grid.Column="2" Text="{Binding MinRatio, StringFormat=F0}" FontSize="11" VerticalAlignment="Center" Foreground="#6B7280"/>
                                                        <TextBlock Grid.Column="3" Text="{Binding MaxRatio, StringFormat=F0}" FontSize="11" VerticalAlignment="Center" Foreground="#6B7280"/>
                                                    </Grid>
                                                </DataTemplate>
                                            </ItemsControl.ItemTemplate>
                                        </ItemsControl>
                                    </StackPanel>
                                </ScrollViewer>
                            </Border>
                        </StackPanel>
                    </Grid>
                </ScrollViewer>
            </Border>

            <!-- 右侧：操作控制区 -->
            <Border Grid.Column="1" Background="#F8FAFC" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="8" Margin="10,0,0,0" Padding="15">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                    <!-- 优化目标选择 -->
                    <TextBlock Text="优化目标选择" FontSize="16" FontWeight="Bold" Foreground="#1E3A8A" Margin="0,0,0,15"/>
                    
                    <StackPanel Margin="0,0,0,25">
                        <RadioButton Content="成本最优" FontSize="14" FontWeight="Medium" IsChecked="{Binding Parameters.OptimizationType, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=CostOptimal}" Margin="0,0,0,10" Foreground="#374151"/>
                        <TextBlock Text="• 优先降低吨矿成本" FontSize="11" Foreground="#6B7280" Margin="20,0,0,3"/>
                        <TextBlock Text="• 在约束条件范围内寻找最低成本解" FontSize="11" Foreground="#6B7280" Margin="20,0,0,15"/>
                        
                        <RadioButton Content="质量最优" FontSize="14" FontWeight="Medium" IsChecked="{Binding Parameters.OptimizationType, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=QualityOptimal}" Foreground="#374151"/>
                        <TextBlock Text="• 优先缩小成分偏差" FontSize="11" Foreground="#6B7280" Margin="20,0,0,3"/>
                        <TextBlock Text="• 严格按照约束条件寻找最优质量解" FontSize="11" Foreground="#6B7280" Margin="20,0,0,0"/>
                    </StackPanel>

                    <!-- 算法选择 -->
                    <TextBlock Text="算法选择" FontSize="16" FontWeight="Bold" Foreground="#1E3A8A" Margin="0,0,0,15"/>
                    
                    <ComboBox SelectedItem="{Binding Parameters.AlgorithmType}" FontSize="13" Height="35" Margin="0,0,0,10" Padding="8" BorderBrush="#D1D5DB" BorderThickness="1">
                        <ComboBoxItem Content="SQP二次序列算法"/>
                        <ComboBoxItem Content="遗传算法（预留）" IsEnabled="False"/>
                        <ComboBoxItem Content="粒子群算法（预留）" IsEnabled="False"/>
                    </ComboBox>
                    
                    <TextBlock Text="• SQP算法，精度±0.01" FontSize="11" Foreground="#6B7280" Margin="0,0,0,3"/>
                    <TextBlock Text="• 适用于非线性约束优化" FontSize="11" Foreground="#6B7280" Margin="0,0,0,3"/>
                    <TextBlock Text="• 收敛速度快，精度高" FontSize="11" Foreground="#6B7280" Margin="0,0,0,25"/>

                    <!-- 操作按钮组 -->
                    <TextBlock Text="操作控制" FontSize="16" FontWeight="Bold" Foreground="#1E3A8A" Margin="0,0,0,15"/>
                    
                    <!-- 物料选择按钮 -->
                    <Button Content="📋 选择参与计算的物料" 
                            Background="#2563EB" 
                            Foreground="White" 
                            FontSize="13" 
                            Height="40" 
                            Margin="0,0,0,15" 
                            BorderThickness="0"
                            Command="{Binding ShowMaterialSelectionCommand}">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" 
                                                    CornerRadius="6" 
                                                    BorderThickness="0">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#1D4ED8"/>
                                                </Trigger>
                                                <Trigger Property="IsPressed" Value="True">
                                                    <Setter Property="Background" Value="#1E40AF"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>
                    
                    <!-- 更新计算按钮 -->
                    <Button Content="🔄 更新计算数据"
                            Style="{StaticResource PrimaryButtonStyle}"
                            Margin="0,0,0,10"
                            Command="{Binding UpdateCalculationCommand}"
                            ToolTip="根据选择的物料更新计算数据"/>

                    <Button Content="🔥 启动算法计算"
                            Style="{StaticResource EnhancedCalculationButtonStyle}"
                            Margin="0,0,0,15"
                            Command="{Binding StartCalculationCommand}"
                            IsEnabled="{Binding IsCalculating, Converter={StaticResource InverseBooleanConverter}}"/>

                    <Button Content="验证约束条件" 
                            Background="#10B981" 
                            Foreground="White" 
                            FontSize="13" 
                            Height="35" 
                            Margin="0,0,0,10" 
                            BorderThickness="0"
                            Command="{Binding ValidateConstraintsCommand}">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" 
                                                    CornerRadius="4" 
                                                    BorderThickness="0">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#059669"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>

                    <Button Content="重置所有参数" 
                            Background="#6B7280" 
                            Foreground="White" 
                            FontSize="13" 
                            Height="35" 
                            Margin="0,0,0,10" 
                            BorderThickness="0"
                            Command="{Binding ResetAllParametersCommand}">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" 
                                                    CornerRadius="4" 
                                                    BorderThickness="0">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#4B5563"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>

                    <Button Content="保存当前配置" 
                            Background="#3B82F6" 
                            Foreground="White" 
                            FontSize="13" 
                            Height="35" 
                            Margin="0,0,0,20" 
                            BorderThickness="0"
                            Command="{Binding SaveCurrentConfigCommand}">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" 
                                                    CornerRadius="4" 
                                                    BorderThickness="0">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#2563EB"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>

                    <!-- 计算进度显示 -->
                    <StackPanel Visibility="{Binding IsCalculating, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <TextBlock Text="算法计算进度" FontSize="14" FontWeight="Bold" Foreground="#1E3A8A" Margin="0,0,0,10"/>
                        <ProgressBar Value="{Binding CalculationProgress}" Height="25" Margin="0,0,0,8"/>
                        <TextBlock Text="{Binding StatusMessage}" HorizontalAlignment="Center" FontSize="12" Foreground="#6B7280"/>
                    </StackPanel>
                </StackPanel>
                </ScrollViewer>
            </Border>
        </Grid>
    </Grid>
</UserControl>
