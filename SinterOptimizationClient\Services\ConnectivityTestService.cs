using System;
using System.Diagnostics;
using System.Net.Http;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace SinterOptimizationClient.Services
{
    /// <summary>
    /// 连接测试服务实现
    /// </summary>
    public class ConnectivityTestService : IConnectivityTestService
    {
        private readonly HttpClient _httpClient;
        private readonly IOptimizationService _optimizationService;

        public ConnectivityTestService(HttpClient httpClient, IOptimizationService optimizationService)
        {
            _httpClient = httpClient;
            _optimizationService = optimizationService;
        }

        public async Task<ConnectivityTestResult> TestFullConnectivityAsync()
        {
            var result = new ConnectivityTestResult();
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                result.TestTimestamp = DateTime.Now;

                // 测试前端到后端连接
                result.FrontendToBackendSuccess = await TestFrontendToBackendAsync();
                
                if (result.FrontendToBackendSuccess)
                {
                    // 获取后端版本信息
                    result.BackendVersion = await GetBackendVersionAsync();
                    
                    // 测试后端到Python连接
                    result.BackendToPythonSuccess = await TestBackendToPythonAsync();
                    
                    if (result.BackendToPythonSuccess)
                    {
                        // 获取Python服务状态
                        result.PythonServiceStatus = await GetPythonServiceStatusAsync();
                    }
                }
                
                result.OverallSuccess = result.FrontendToBackendSuccess && result.BackendToPythonSuccess;
                
                if (!result.OverallSuccess)
                {
                    if (!result.FrontendToBackendSuccess)
                    {
                        result.ErrorMessage = "前端到后端连接失败，请检查后端服务是否正常运行";
                    }
                    else if (!result.BackendToPythonSuccess)
                    {
                        result.ErrorMessage = "后端到Python算法服务连接失败，请检查Python服务是否正常运行";
                    }
                }
                else
                {
                    result.ErrorMessage = "所有连接测试通过";
                }
            }
            catch (Exception ex)
            {
                result.FrontendToBackendSuccess = false;
                result.BackendToPythonSuccess = false;
                result.OverallSuccess = false;
                result.ErrorMessage = $"连接测试异常: {ex.Message}";
            }
            finally
            {
                stopwatch.Stop();
                result.TestDuration = stopwatch.Elapsed;
            }
            
            return result;
        }

        public async Task<bool> TestFrontendToBackendAsync()
        {
            try
            {
                // 使用现有的健康检查接口测试连接
                return await _optimizationService.CheckServiceHealthAsync();
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> TestBackendToPythonAsync()
        {
            try
            {
                // 通过后端API测试Python服务连接
                var response = await _httpClient.GetAsync("health");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var healthData = JsonConvert.DeserializeObject<dynamic>(content);
                    
                    // 检查响应中是否包含Python服务状态信息
                    return healthData?.service != null;
                }
                return false;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<string> GetBackendVersionAsync()
        {
            try
            {
                return await _optimizationService.GetServiceVersionAsync();
            }
            catch (Exception ex)
            {
                return $"获取版本失败: {ex.Message}";
            }
        }

        public async Task<string> GetPythonServiceStatusAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("/");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var data = JsonConvert.DeserializeObject<dynamic>(content);
                    
                    if (data?.status != null)
                    {
                        return $"Python服务状态: {data.status}, 版本: {data.version ?? "未知"}";
                    }
                }
                return "无法获取Python服务状态";
            }
            catch (Exception ex)
            {
                return $"Python服务连接失败: {ex.Message}";
            }
        }
    }
}