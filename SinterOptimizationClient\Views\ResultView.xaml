<UserControl x:Class="SinterOptimizationClient.Views.ResultView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:converters="clr-namespace:SinterOptimizationClient.Converters"
             mc:Ignorable="d" 
             d:DesignHeight="900" d:DesignWidth="1600">

    <UserControl.Resources>
        <!-- 转换器 -->
        <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:BooleanToStringConverter x:Key="BooleanToStringConverter"/>
        <converters:BooleanToColorConverter x:Key="BooleanToColorConverter"/>
        
        <!-- 基础样式 -->
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2563EB"/>
        <SolidColorBrush x:Key="LightGrayBrush" Color="#F8F9FA"/>
        <SolidColorBrush x:Key="BorderBrush" Color="#E5E7EB"/>
        
        <!-- 按钮样式 -->
        <Style x:Key="NavigationButtonStyle" TargetType="Button">
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#F97316"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="SecondaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#6B7280"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid Background="{StaticResource LightGrayBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题区域 -->
        <Border Grid.Row="0" Background="White" BorderBrush="{StaticResource BorderBrush}" BorderThickness="0,0,0,1" Padding="20,15">
            <StackPanel>
                <TextBlock Text="优化计算结果" FontSize="20" FontWeight="Bold" Foreground="{StaticResource PrimaryBrush}" Margin="0,0,0,10"/>
                
                <!-- 切换按钮 -->
                <StackPanel Orientation="Horizontal">
                    <Button Width="120" Height="32" Margin="0,0,10,0"
                            Command="{Binding SwitchToCostOptimalCommand}"
                            Style="{StaticResource NavigationButtonStyle}">
                        <Button.Background>
                            <SolidColorBrush Color="#F97316"/>
                        </Button.Background>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="💰" FontSize="12" Margin="0,0,5,0"/>
                            <TextBlock Text="成本最优结果" FontSize="11" Foreground="White"/>
                        </StackPanel>
                    </Button>
                    <Button Width="120" Height="32"
                            Command="{Binding SwitchToQualityOptimalCommand}"
                            Style="{StaticResource NavigationButtonStyle}">
                        <Button.Background>
                            <SolidColorBrush Color="#10B981"/>
                        </Button.Background>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🎯" FontSize="12" Margin="0,0,5,0"/>
                            <TextBlock Text="质量最优结果" FontSize="11" Foreground="White"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- 主要内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="0.6*"/>
                <ColumnDefinition Width="0.4*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧：结果表格 -->
            <Border Grid.Column="0" Background="White" BorderBrush="{StaticResource BorderBrush}" BorderThickness="0,0,1,0" Padding="20">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 结果表格 -->
                    <DataGrid Grid.Row="0"
                              ItemsSource="{Binding CurrentSolutions}"
                              SelectedItem="{Binding SelectedSolution}"
                              AutoGenerateColumns="False"
                              CanUserAddRows="False"
                              CanUserDeleteRows="False"
                              IsReadOnly="True"
                              GridLinesVisibility="Horizontal"
                              HeadersVisibility="Column"
                              Background="White"
                              BorderBrush="{StaticResource BorderBrush}"
                              BorderThickness="1"
                              Margin="0,0,0,15"
                              RowHeight="120">

                        <DataGrid.Columns>
                            <DataGridTextColumn Header="方案" Binding="{Binding SolutionId}" Width="60">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="VerticalAlignment" Value="Top"/>
                                        <Setter Property="Margin" Value="5,10,5,5"/>
                                        <Setter Property="FontSize" Value="12"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="湿配比(%)" Binding="{Binding FormattedWetRatios}" Width="280">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="TextWrapping" Value="Wrap"/>
                                        <Setter Property="VerticalAlignment" Value="Top"/>
                                        <Setter Property="Margin" Value="5,10,5,5"/>
                                        <Setter Property="FontSize" Value="11"/>
                                        <Setter Property="FontFamily" Value="Consolas, 'Courier New', monospace"/>
                                        <Setter Property="LineHeight" Value="16"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="成本(元/吨)" Binding="{Binding Cost, StringFormat=F1}" Width="100">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="VerticalAlignment" Value="Top"/>
                                        <Setter Property="Margin" Value="5,10,5,5"/>
                                        <Setter Property="FontSize" Value="12"/>
                                        <Setter Property="FontWeight" Value="Medium"/>
                                        <Setter Property="Foreground" Value="#F97316"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="TFe(%)" Binding="{Binding TFe, StringFormat=F1}" Width="80">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="VerticalAlignment" Value="Top"/>
                                        <Setter Property="Margin" Value="5,10,5,5"/>
                                        <Setter Property="FontSize" Value="12"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="R" Binding="{Binding R, StringFormat=F2}" Width="80">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="VerticalAlignment" Value="Top"/>
                                        <Setter Property="Margin" Value="5,10,5,5"/>
                                        <Setter Property="FontSize" Value="12"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTemplateColumn Header="操作" Width="80">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Button Content="查看详情"
                                                Style="{StaticResource PrimaryButtonStyle}"
                                                Height="28"
                                                FontSize="11"
                                                Margin="5,10,5,5"
                                                VerticalAlignment="Top"
                                                Command="{Binding DataContext.ViewSolutionDetailsCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                CommandParameter="{Binding}"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- 状态栏 -->
                    <Border Grid.Row="1" Background="{StaticResource PrimaryBrush}" Padding="10,5">
                        <TextBlock Text="{Binding StatusMessage}" Foreground="White" FontSize="12"/>
                    </Border>
                </Grid>
            </Border>

            <!-- 右侧：可视化展示 -->
            <Border Grid.Column="1" Background="#F9FAFB" Padding="15">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <!-- 标题 -->
                        <TextBlock Text="优化结果可视化" FontSize="16" FontWeight="Bold" Foreground="{StaticResource PrimaryBrush}" Margin="0,0,0,15"/>

                        <!-- 优化方案选择标签 -->
                        <Border Background="White" CornerRadius="6" Padding="5" Margin="0,0,0,15">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <RadioButton Name="CostOptimalTab" Content="成本最优方案" FontSize="13" FontWeight="Medium"
                                           IsChecked="True" Margin="0,0,20,0" Foreground="#374151"/>
                                <RadioButton Name="QualityOptimalTab" Content="质量最优方案" FontSize="13" FontWeight="Medium"
                                           Foreground="#374151"/>
                            </StackPanel>
                        </Border>

                        <!-- 成本最优方案可视化 -->
                        <StackPanel Name="CostOptimalVisualization" Visibility="{Binding ElementName=CostOptimalTab, Path=IsChecked, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <!-- 成本最优 - 成分达标情况 -->
                            <Border Background="White" CornerRadius="6" Padding="12" Margin="0,0,0,10">
                                <StackPanel>
                                    <TextBlock Text="成本最优 - 成分达标情况" FontSize="13" FontWeight="Bold" Margin="0,0,0,10" Foreground="#F97316"/>
                                    <Grid Height="100">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- TFe -->
                                        <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                            <Rectangle Fill="#F97316" Width="25" Height="60" Margin="0,0,0,5"/>
                                            <TextBlock Text="TFe" FontSize="10" HorizontalAlignment="Center"/>
                                            <TextBlock Text="54.8%" FontSize="9" HorizontalAlignment="Center" Foreground="#666"/>
                                        </StackPanel>

                                        <!-- R -->
                                        <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                            <Rectangle Fill="#F97316" Width="25" Height="70" Margin="0,0,0,5"/>
                                            <TextBlock Text="R" FontSize="10" HorizontalAlignment="Center"/>
                                            <TextBlock Text="1.89" FontSize="9" HorizontalAlignment="Center" Foreground="#666"/>
                                        </StackPanel>

                                        <!-- MgO -->
                                        <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                            <Rectangle Fill="#F59E0B" Width="25" Height="45" Margin="0,0,0,5"/>
                                            <TextBlock Text="MgO" FontSize="10" HorizontalAlignment="Center"/>
                                            <TextBlock Text="2.35%" FontSize="9" HorizontalAlignment="Center" Foreground="#666"/>
                                        </StackPanel>

                                        <!-- Al2O3 -->
                                        <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                                            <Rectangle Fill="#F97316" Width="25" Height="55" Margin="0,0,0,5"/>
                                            <TextBlock Text="Al₂O₃" FontSize="10" HorizontalAlignment="Center"/>
                                            <TextBlock Text="1.85%" FontSize="9" HorizontalAlignment="Center" Foreground="#666"/>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </Border>

                            <!-- 成本最优 - 成本构成分析 -->
                            <Border Background="White" CornerRadius="6" Padding="12" Margin="0,0,0,10">
                                <StackPanel>
                                    <TextBlock Text="成本最优 - 成本构成分析" FontSize="13" FontWeight="Bold" Margin="0,0,0,10" Foreground="#F97316"/>
                                    <Grid Height="80">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 成本信息 -->
                                        <StackPanel Grid.Column="0" VerticalAlignment="Center">
                                            <TextBlock Text="总成本" FontSize="12" FontWeight="Bold" Margin="0,0,0,5"/>
                                            <TextBlock Text="658.1 元/吨" FontSize="16" FontWeight="Bold" Foreground="#F97316"/>
                                            <TextBlock Text="成本最优方案" FontSize="10" Foreground="#666" Margin="0,2,0,0"/>
                                        </StackPanel>

                                        <!-- 成本优势 -->
                                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                            <TextBlock Text="成本节约" FontSize="12" FontWeight="Bold" Margin="0,0,0,5"/>
                                            <TextBlock Text="21.9 元/吨" FontSize="14" FontWeight="Bold" Foreground="#10B981"/>
                                            <TextBlock Text="相比质量最优" FontSize="10" Foreground="#666" Margin="0,2,0,0"/>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </Border>
                        </StackPanel>

                        <!-- 质量最优方案可视化 -->
                        <StackPanel Name="QualityOptimalVisualization" Visibility="{Binding ElementName=QualityOptimalTab, Path=IsChecked, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <!-- 质量最优 - 成分达标情况 -->
                            <Border Background="White" CornerRadius="6" Padding="12" Margin="0,0,0,10">
                                <StackPanel>
                                    <TextBlock Text="质量最优 - 成分达标情况" FontSize="13" FontWeight="Bold" Margin="0,0,0,10" Foreground="#10B981"/>
                                    <Grid Height="100">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- TFe -->
                                        <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                            <Rectangle Fill="#10B981" Width="25" Height="60" Margin="0,0,0,5"/>
                                            <TextBlock Text="TFe" FontSize="10" HorizontalAlignment="Center"/>
                                            <TextBlock Text="55.0%" FontSize="9" HorizontalAlignment="Center" Foreground="#666"/>
                                        </StackPanel>

                                        <!-- R -->
                                        <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                            <Rectangle Fill="#10B981" Width="25" Height="70" Margin="0,0,0,5"/>
                                            <TextBlock Text="R" FontSize="10" HorizontalAlignment="Center"/>
                                            <TextBlock Text="1.90" FontSize="9" HorizontalAlignment="Center" Foreground="#666"/>
                                        </StackPanel>

                                        <!-- MgO -->
                                        <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                            <Rectangle Fill="#10B981" Width="25" Height="45" Margin="0,0,0,5"/>
                                            <TextBlock Text="MgO" FontSize="10" HorizontalAlignment="Center"/>
                                            <TextBlock Text="2.39%" FontSize="9" HorizontalAlignment="Center" Foreground="#666"/>
                                        </StackPanel>

                                        <!-- Al2O3 -->
                                        <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                                            <Rectangle Fill="#10B981" Width="25" Height="55" Margin="0,0,0,5"/>
                                            <TextBlock Text="Al₂O₃" FontSize="10" HorizontalAlignment="Center"/>
                                            <TextBlock Text="1.89%" FontSize="9" HorizontalAlignment="Center" Foreground="#666"/>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </Border>

                            <!-- 质量最优 - 质量偏差分析 -->
                            <Border Background="White" CornerRadius="6" Padding="12" Margin="0,0,0,10">
                                <StackPanel>
                                    <TextBlock Text="质量最优 - 质量偏差分析" FontSize="13" FontWeight="Bold" Margin="0,0,0,10" Foreground="#10B981"/>
                                    <Grid Height="80">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 质量偏差 -->
                                        <StackPanel Grid.Column="0" VerticalAlignment="Center">
                                            <TextBlock Text="总偏差" FontSize="12" FontWeight="Bold" Margin="0,0,0,5"/>
                                            <TextBlock Text="±0.05%" FontSize="16" FontWeight="Bold" Foreground="#10B981"/>
                                            <TextBlock Text="质量最优方案" FontSize="10" Foreground="#666" Margin="0,2,0,0"/>
                                        </StackPanel>

                                        <!-- 成本信息 -->
                                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                            <TextBlock Text="总成本" FontSize="12" FontWeight="Bold" Margin="0,0,0,5"/>
                                            <TextBlock Text="680.0 元/吨" FontSize="14" FontWeight="Bold" Foreground="#6B7280"/>
                                            <TextBlock Text="在成本上限内" FontSize="10" Foreground="#666" Margin="0,2,0,0"/>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </Border>
                        </StackPanel>

                        <!-- 配比分布 -->
                        <Border Background="White" CornerRadius="6" Padding="12" Margin="0,0,0,10">
                            <StackPanel>
                                <TextBlock Text="主要物料配比" FontSize="13" FontWeight="Bold" Margin="0,0,0,10"/>
                                <StackPanel>
                                    <!-- 印粉海娜 -->
                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="80"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="50"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="印粉海娜" FontSize="11" VerticalAlignment="Center"/>
                                        <Rectangle Grid.Column="1" Fill="#6366F1" Height="12" Margin="5,0" HorizontalAlignment="Left" Width="80"/>
                                        <TextBlock Grid.Column="2" Text="10.4%" FontSize="11" VerticalAlignment="Center"/>
                                    </Grid>
                                    
                                    <!-- 俄罗斯精粉 -->
                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="80"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="50"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="俄罗斯精粉" FontSize="11" VerticalAlignment="Center"/>
                                        <Rectangle Grid.Column="1" Fill="#8B5CF6" Height="12" Margin="5,0" HorizontalAlignment="Left" Width="60"/>
                                        <TextBlock Grid.Column="2" Text="8.0%" FontSize="11" VerticalAlignment="Center"/>
                                    </Grid>
                                    
                                    <!-- 高炉返矿 -->
                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="80"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="50"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="高炉返矿" FontSize="11" VerticalAlignment="Center"/>
                                        <Rectangle Grid.Column="1" Fill="#06B6D4" Height="12" Margin="5,0" HorizontalAlignment="Left" Width="70"/>
                                        <TextBlock Grid.Column="2" Text="8.0%" FontSize="11" VerticalAlignment="Center"/>
                                    </Grid>
                                    
                                    <!-- 生石灰 -->
                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="80"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="50"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="生石灰" FontSize="11" VerticalAlignment="Center"/>
                                        <Rectangle Grid.Column="1" Fill="#10B981" Height="12" Margin="5,0" HorizontalAlignment="Left" Width="50"/>
                                        <TextBlock Grid.Column="2" Text="6.5%" FontSize="11" VerticalAlignment="Center"/>
                                    </Grid>
                                </StackPanel>
                            </StackPanel>
                        </Border>

                        <!-- 算法信息 -->
                        <Border Background="White" CornerRadius="6" Padding="12">
                            <StackPanel>
                                <TextBlock Text="算法信息" FontSize="13" FontWeight="Bold" Margin="0,0,0,10"/>
                                <StackPanel>
                                    <TextBlock Text="算法类型：SQP二次序列算法" FontSize="11" Margin="0,0,0,3"/>
                                    <TextBlock Text="计算精度：±0.01%" FontSize="11" Margin="0,0,0,3"/>
                                    <TextBlock Text="迭代次数：2轮" FontSize="11" Margin="0,0,0,3"/>
                                    <TextBlock Text="计算状态：优化成功" FontSize="11" Foreground="#10B981"/>
                                </StackPanel>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </ScrollViewer>
            </Border>
        </Grid>
    </Grid>
</UserControl>
